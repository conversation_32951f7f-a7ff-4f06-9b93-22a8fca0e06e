"use client";

import { useState, useEffect, useCallback } from 'react';
import { readMe } from '@directus/sdk';
import client from './directus';
import type { 
  AuthenticationState, 
  LoginCredentials, 
  AuthError,
  AuthenticationResponse 
} from './types';

/**
 * Custom hook for managing Directus authentication state
 * Provides login, logout, and authentication status management
 */
export function useAuth() {
  const [authState, setAuthState] = useState<AuthenticationState>({
    isAuthenticated: false,
    isLoading: true,
    user: null,
    error: null,
  });

  /**
   * Check if user is currently authenticated by attempting to fetch user data
   */
  const checkAuthStatus = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      
      // Try to get current user data to verify authentication
      const user = await client.request(readMe());
      
      setAuthState({
        isAuthenticated: true,
        isLoading: false,
        user,
        error: null,
      });
      
      return true;
    } catch (error) {
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        error: null, // Don't show error for unauthenticated state
      });
      
      return false;
    }
  }, []);

  /**
   * Login user with email and password
   */
  const login = useCallback(async (credentials: LoginCredentials): Promise<AuthenticationResponse> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Validate credentials
      if (!credentials.email || !credentials.password) {
        throw new Error('Email and password are required');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(credentials.email)) {
        throw new Error('Please enter a valid email address');
      }

      // Attempt login using Directus SDK
      const response = await client.login({
        email: credentials.email,
        password: credentials.password,
      });

      // Fetch user data after successful login
      const user = await client.request(readMe());

      setAuthState({
        isAuthenticated: true,
        isLoading: false,
        user,
        error: null,
      });

      return response;
    } catch (error: any) {
      let errorMessage = 'Login failed. Please try again.';
      
      // Handle specific Directus error codes
      if (error?.errors?.[0]?.extensions?.code === 'INVALID_CREDENTIALS') {
        errorMessage = 'Invalid email or password. Please check your credentials and try again.';
      } else if (error?.errors?.[0]?.extensions?.code === 'USER_SUSPENDED') {
        errorMessage = 'Your account has been suspended. Please contact support.';
      } else if (error?.errors?.[0]?.extensions?.code === 'INVALID_PAYLOAD') {
        errorMessage = 'Invalid login data. Please check your email and password.';
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (error?.code === 'NETWORK_ERROR' || !navigator.onLine) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      }

      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));

      throw new Error(errorMessage);
    }
  }, []);

  /**
   * Logout user and clear authentication state
   */
  const logout = useCallback(async (): Promise<void> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Call Directus logout to invalidate tokens
      await client.logout();

      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        error: null,
      });
    } catch (error: any) {
      // Even if logout fails on server, clear local state
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        error: 'Logout completed, but there was an issue clearing server session.',
      });
      
      console.warn('Logout warning:', error);
    }
  }, []);

  /**
   * Clear any authentication errors
   */
  const clearError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }));
  }, []);

  /**
   * Refresh authentication status
   */
  const refresh = useCallback(async (): Promise<void> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      await client.refresh();
      await checkAuthStatus();
    } catch (error) {
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        error: 'Session expired. Please log in again.',
      });
    }
  }, [checkAuthStatus]);

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  return {
    ...authState,
    login,
    logout,
    refresh,
    clearError,
    checkAuthStatus,
  };
}

/**
 * Utility function to get current authentication token
 */
export async function getAuthToken(): Promise<string | null> {
  try {
    return await client.getToken();
  } catch (error) {
    return null;
  }
}

/**
 * Utility function to check if user is authenticated (without React hooks)
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    await client.request(readMe());
    return true;
  } catch (error) {
    return false;
  }
}
