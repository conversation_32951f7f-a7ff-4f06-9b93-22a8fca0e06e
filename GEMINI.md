# Project Overview

This is a Next.js project bootstrapped with `create-next-app`. It appears to be an event management application that uses Directus as a backend or CMS. The application is built with React and uses Tailwind CSS for styling.

## Key Technologies

*   **Framework:** Next.js
*   **Language:** TypeScript
*   **Styling:** Tailwind CSS
*   **Backend/CMS:** Directus
*   **Data Fetching:** @tanstack/react-query, @directus/sdk
*   **State Management:** Zustand

## Building and Running

To get the development environment running, use the following command:

```bash
npm run dev
```

This will start the development server on [http://localhost:3000](http://localhost:3000).

Other available scripts:

*   `npm run build`: Creates a production build of the application.
*   `npm run start`: Starts the production server.
*   `npm run lint`: Lints the codebase using ESLint.

## Development Conventions

*   The project follows the standard Next.js project structure.
*   The `src` directory contains the main application code.
*   The `src/lib` directory contains utility functions, Directus client setup, and type definitions.
*   The `src/app` directory contains the pages and layouts of the application.
*   The project uses TypeScript for static typing.
*   The data model is defined in `src/lib/types.ts`, which corresponds to the collections in Directus.

### 📝 General

* UI/error text → **Indonesian**.
* Do **not** translate `src/components/ui/` base text.
* Global progress bar → `bprogress/next`.
* No `any` type in TypeScript.


## MCP Servers
always use context7 to get the related context before any tasks.