"use client";

import { useState } from 'react';
import { useAuth } from '@/lib/auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, CheckCircle, User, Mail, Calendar, Shield, Loader2 } from 'lucide-react';

/**
 * Comprehensive Directus Authentication Test Page
 * 
 * This page demonstrates and validates the Directus authentication flow using the official SDK.
 * Features:
 * - Login form with email/password validation
 * - Real-time authentication status display
 * - User information display when authenticated
 * - Comprehensive error handling
 * - Loading states for better UX
 * - Logout functionality
 * - Token management demonstration
 */
export default function TestAuthPage() {
  const {
    isAuthenticated,
    isLoading,
    user,
    error,
    login,
    logout,
    clearError,
    refresh,
  } = useAuth();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lastLoginTime, setLastLoginTime] = useState<string | null>(null);

  /**
   * Validate form inputs
   */
  const validateForm = (): boolean => {
    const errors: {[key: string]: string} = {};

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.password.trim()) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 3) {
      errors.password = 'Password must be at least 3 characters long';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  /**
   * Handle form submission for login
   */
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    clearError();

    try {
      await login({
        email: formData.email.trim(),
        password: formData.password,
      });
      
      setLastLoginTime(new Date().toLocaleString());
      setFormData({ email: '', password: '' });
      setFormErrors({});
    } catch (error) {
      // Error is already handled in the useAuth hook
      console.error('Login failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Handle logout
   */
  const handleLogout = async () => {
    try {
      await logout();
      setLastLoginTime(null);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  /**
   * Handle input changes
   */
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field-specific error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
    
    // Clear general error when user interacts with form
    if (error) {
      clearError();
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="mx-auto max-w-4xl space-y-6">
        {/* Page Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">
            Directus Authentication Test
          </h1>
          <p className="text-muted-foreground">
            Comprehensive testing interface for Directus SDK authentication flow
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Authentication Status Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Authentication Status
              </CardTitle>
              <CardDescription>
                Real-time authentication state and user information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Status Indicator */}
              <div className="flex items-center gap-2">
                <div className={`h-3 w-3 rounded-full ${
                  isLoading 
                    ? 'bg-yellow-500 animate-pulse' 
                    : isAuthenticated 
                      ? 'bg-green-500' 
                      : 'bg-red-500'
                }`} />
                <span className="font-medium">
                  {isLoading 
                    ? 'Checking authentication...' 
                    : isAuthenticated 
                      ? 'Authenticated' 
                      : 'Not authenticated'
                  }
                </span>
              </div>

              {/* User Information */}
              {isAuthenticated && user && (
                <div className="space-y-3 p-4 bg-muted rounded-lg">
                  <h4 className="font-semibold flex items-center gap-2">
                    <User className="h-4 w-4" />
                    User Information
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>Email: {user.email}</span>
                    </div>
                    {user.first_name && (
                      <div>First Name: {user.first_name}</div>
                    )}
                    {user.last_name && (
                      <div>Last Name: {user.last_name}</div>
                    )}
                    <div>User ID: {user.id}</div>
                    {user.role && (
                      <div>Role: {typeof user.role === 'object' ? user.role.name : user.role}</div>
                    )}
                    {lastLoginTime && (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>Last Login: {lastLoginTime}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2">
                {isAuthenticated ? (
                  <Button 
                    onClick={handleLogout}
                    variant="destructive"
                    disabled={isLoading}
                  >
                    {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Logout
                  </Button>
                ) : null}
                
                <Button 
                  onClick={refresh}
                  variant="outline"
                  disabled={isLoading}
                >
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Refresh Status
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Login Form Card */}
          {!isAuthenticated && (
            <Card>
              <CardHeader>
                <CardTitle>Login</CardTitle>
                <CardDescription>
                  Enter your Directus credentials to test authentication
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleLogin} className="space-y-4">
                  {/* Email Field */}
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      disabled={isSubmitting}
                      className={formErrors.email ? 'border-destructive' : ''}
                    />
                    {formErrors.email && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {formErrors.email}
                      </p>
                    )}
                  </div>

                  {/* Password Field */}
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter your password"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      disabled={isSubmitting}
                      className={formErrors.password ? 'border-destructive' : ''}
                    />
                    {formErrors.password && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {formErrors.password}
                      </p>
                    )}
                  </div>

                  {/* Submit Button */}
                  <Button 
                    type="submit" 
                    className="w-full"
                    disabled={isSubmitting || isLoading}
                  >
                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {isSubmitting ? 'Signing in...' : 'Sign In'}
                  </Button>
                </form>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <Card className="border-destructive">
            <CardContent className="pt-6">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-5 w-5 text-destructive mt-0.5" />
                <div className="space-y-1">
                  <p className="font-medium text-destructive">Authentication Error</p>
                  <p className="text-sm text-muted-foreground">{error}</p>
                </div>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={clearError}
                  className="ml-auto"
                >
                  Dismiss
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Success Message */}
        {isAuthenticated && !isLoading && (
          <Card className="border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <p className="font-medium text-green-800">
                  Successfully authenticated with Directus!
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Technical Information Card */}
        <Card>
          <CardHeader>
            <CardTitle>Technical Implementation Details</CardTitle>
            <CardDescription>
              Information about the authentication implementation and security practices
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="font-semibold">Authentication Method</h4>
                <p className="text-sm text-muted-foreground">
                  Using official Directus SDK with JSON authentication mode
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">Token Storage</h4>
                <p className="text-sm text-muted-foreground">
                  Managed by Directus SDK (in-memory for security)
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">Security Features</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Automatic token refresh</li>
                  <li>• Secure logout with token invalidation</li>
                  <li>• Input validation and sanitization</li>
                  <li>• Error handling for various scenarios</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">Error Handling</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Invalid credentials detection</li>
                  <li>• Network error handling</li>
                  <li>• User suspension detection</li>
                  <li>• Session expiration management</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Testing Scenarios Card */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Scenarios</CardTitle>
            <CardDescription>
              Suggested test cases to validate the authentication implementation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-semibold">Valid Credentials Test</h4>
                <p className="text-sm text-muted-foreground">
                  Test with valid Directus user credentials to verify successful authentication flow.
                </p>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">Invalid Credentials Test</h4>
                <p className="text-sm text-muted-foreground">
                  Test with incorrect email/password to verify proper error handling and user feedback.
                </p>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">Form Validation Test</h4>
                <p className="text-sm text-muted-foreground">
                  Test with empty fields, invalid email formats, and short passwords to verify client-side validation.
                </p>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">Network Error Test</h4>
                <p className="text-sm text-muted-foreground">
                  Test with network disconnected to verify network error handling.
                </p>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">Session Management Test</h4>
                <p className="text-sm text-muted-foreground">
                  Test logout functionality and verify that authentication state is properly cleared.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-muted-foreground">
          <p>
            This test page demonstrates the Directus authentication flow using the official SDK v20.0.3
          </p>
          <p className="mt-1">
            Built with Next.js App Router, TypeScript, and shadcn/ui components
          </p>
        </div>
      </div>
    </div>
  );
}
