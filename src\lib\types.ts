import type {
  Directus<PERSON><PERSON>,
  DirectusUser,
  <PERSON><PERSON><PERSON><PERSON>,
  DirectusFolder,
  DirectusNotification,
} from "@directus/sdk";

export interface CustomDirectusTypes {
  announcement: Announcement[];
  card: Card[];
  card_template: CardTemplate[];
  event: Event[];
  event_directus_users: EventDirectusUser[];
  event_files: EventFile[];
  guest: Guest[];
  guest_undefined: GuestUndefined[];
  print: Print[];
  template: Template[];
  directus_users: DirectusUser[];
  directus_roles: DirectusRole[];
  directus_folders: DirectusFolder[];
  directus_files: DirectusFile[];
  directus_notifications: DirectusNotification[];
}

export interface Announcement {
  id: number;
  date_created: string | null;
  date_updated: string | null;
  title: string | null;
  type: "default" | "info" | "success" | "warning" | "error" | null;
  event: number | Event | null;
  content: string | null;
}

export interface Card {
  id: number;
  photo: string | DirectusFile<CustomDirectusTypes> | null;
  name: string | null;
  title: string | null;
  time: string | null;
  place: string | null;
  info: string | null;
  background: string | DirectusFile<CustomDirectusTypes> | null;
  event: number | Event | null;
  greeting: string | null;
  color_background: string | null;
  color_text: string | null;
  color_title: string | null;
  color_background_title: string | null;
  card_pic: string | DirectusFile<CustomDirectusTypes> | null;
  name_font: string | null;
  date: string | null;
  color_name: string | null;
  text_font: string | null;
  language: 0 | 1 | null;
  session: 0 | 1 | null;
}

export interface CardTemplate {
  id: number;
  user_created: string | DirectusUser<CustomDirectusTypes> | null;
  date_created: string | null;
  user_updated: string | DirectusUser<CustomDirectusTypes> | null;
  date_updated: string | null;
  name: string | null;
  slug: string | null;
  event: number | Event | null;
  canvas_json: unknown | null;
  width_mm: number | null;
  height_mm: number | null;
  bleed_mm: number | null;
  fonts: unknown | null;
}

export interface Event {
  id: number;
  sort: number | null;
  date_created: string | null;
  date_updated: string | null;
  title: string;
  location: string | null;
  start: string | null;
  url_post: string | null;
  greeting_screen: boolean | null;
  greeting_background: string | null;
  photo: boolean | null;
  scanner_pic: string | DirectusFile<CustomDirectusTypes> | null;
  cover: string | DirectusFile<CustomDirectusTypes> | null;
  code_event: string | null;
  card: number | Card | null;
  print_template: number | Print | null;
  print_feature: boolean | null;
  users: number[] | EventDirectusUser[];
  selfies: number[] | EventFile[];
}

export interface EventDirectusUser {
  id: number;
  event_id: number | Event | null;
  directus_users_id: string | DirectusUser<CustomDirectusTypes> | null;
}

export interface EventFile {
  id: number;
  event_id: number | Event | null;
  directus_files_id: string | DirectusFile<CustomDirectusTypes> | null;
}

export interface Guest {
  id: number;
  date_created: string | null;
  date_updated: string | null;
  code_guest: string;
  name: string | null;
  status_relation: string | null;
  shift: string | null;
  phone: string | null;
  table: string | null;
  address: string | null;
  level: 1 | 2 | 3 | null;
  gift: Array<"1" | "2" | "3"> | null;
  presence: boolean | null;
  amount_guest: number | null;
  attendance_time: string | null;
  take_souvenir: boolean | null;
  amount_souvenir: number | null;
  label: string | null;
  type_invitation: string | null;
  bride: "1" | "2" | null;
  note: string | null;
  event: number | Event | null;
  regist_by: string | DirectusUser<CustomDirectusTypes> | null;
  on_Site: boolean | null;
  entrust: boolean | null;
  entrust_by: string | null;
  category: string | null;
  sent: boolean | null;
  rsvp: "0" | "1" | string | null;
  max_pax: string | null;
  selfie: string | DirectusFile<CustomDirectusTypes> | null;
  wish: string | null;
}

export interface GuestUndefined {
  id: number;
  guest_id: number | null;
  item: string | null;
  collection: string | null;
}

export interface Print {
  id: number;
  template_general: string | null;
  template_guest: string | null;
}

export interface Template {
  id: number;
  title: string | null;
  content: string | null;
  event: number | Event | null;
}

// GeoJSON Types

export interface GeoJSONPoint {
  type: "Point";
  coordinates: [number, number];
}

export interface GeoJSONLineString {
  type: "LineString";
  coordinates: Array<[number, number]>;
}

export interface GeoJSONPolygon {
  type: "Polygon";
  coordinates: Array<Array<[number, number]>>;
}

export interface GeoJSONMultiPoint {
  type: "MultiPoint";
  coordinates: Array<[number, number]>;
}

export interface GeoJSONMultiLineString {
  type: "MultiLineString";
  coordinates: Array<Array<[number, number]>>;
}

export interface GeoJSONMultiPolygon {
  type: "MultiPolygon";
  coordinates: Array<Array<Array<[number, number]>>>;
}

export interface GeoJSONGeometryCollection {
  type: "GeometryCollection";
  geometries: Array<
    | GeoJSONPoint
    | GeoJSONLineString
    | GeoJSONPolygon
    | GeoJSONMultiPoint
    | GeoJSONMultiLineString
    | GeoJSONMultiPolygon
  >;
}

// Authentication-related types
export interface AuthenticationResponse {
  access_token: string;
  refresh_token: string;
  expires: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthenticationState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: DirectusUser<CustomDirectusTypes> | null;
  error: string | null;
}

export interface AuthError {
  code: string;
  message: string;
  extensions?: {
    code: string;
    [key: string]: any;
  };
}

export type AuthenticationStatus =
  | "idle"
  | "loading"
  | "authenticated"
  | "unauthenticated"
  | "error";
