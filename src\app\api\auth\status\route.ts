import { NextRequest, NextResponse } from 'next/server';
import { readMe } from '@directus/sdk';
import client from '@/lib/directus';

/**
 * API Route for checking authentication status
 * 
 * This endpoint provides a server-side way to verify authentication status
 * and can be used for additional testing scenarios or SSR authentication checks.
 * 
 * GET /api/auth/status
 * Returns: { authenticated: boolean, user?: object, error?: string }
 */
export async function GET(request: NextRequest) {
  try {
    // Attempt to get current user data to verify authentication
    const user = await client.request(readMe());
    
    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        role: user.role,
      },
    });
  } catch (error: any) {
    // User is not authenticated or token is invalid
    return NextResponse.json({
      authenticated: false,
      error: error?.message || 'Not authenticated',
    }, { status: 401 });
  }
}

/**
 * POST /api/auth/status
 * 
 * Alternative endpoint that accepts a token in the request body
 * for testing token validation scenarios
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token } = body;

    if (!token) {
      return NextResponse.json({
        authenticated: false,
        error: 'No token provided',
      }, { status: 400 });
    }

    // Set token temporarily for this request
    await client.setToken(token);
    
    // Try to get user data with the provided token
    const user = await client.request(readMe());
    
    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        role: user.role,
      },
      token_valid: true,
    });
  } catch (error: any) {
    return NextResponse.json({
      authenticated: false,
      token_valid: false,
      error: error?.message || 'Invalid token',
    }, { status: 401 });
  }
}
